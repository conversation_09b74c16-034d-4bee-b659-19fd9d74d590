# Server Deployment Guide for Chrome Bot Automation

This guide explains how to deploy your Chrome-based bot automation system on a server environment.

## Overview

Your bot has been updated to automatically detect the environment and configure Chrome accordingly:
- **Development**: Runs Chrome with visible browser window
- **Production/Server**: Runs Chrome in headless mode (background)

## Server Requirements

### 1. Chrome/Chromium Installation

#### Ubuntu/Debian Servers:
```bash
# Update package list
sudo apt update

# Install Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install google-chrome-stable

# Or install Chromium (lighter alternative)
sudo apt install chromium-browser
```

#### CentOS/RHEL Servers:
```bash
# Install Chrome
sudo yum install -y wget
wget https://dl.google.com/linux/direct/google-chrome-stable_current_x86_64.rpm
sudo yum localinstall google-chrome-stable_current_x86_64.rpm
```

### 2. ChromeDriver Installation

```bash
# Download ChromeDriver (check Chrome version first)
CHROME_VERSION=$(google-chrome --version | cut -d ' ' -f3 | cut -d '.' -f1)
wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION}/chromedriver_linux64.zip"

# Extract and install
sudo unzip /tmp/chromedriver.zip -d /usr/local/bin/
sudo chmod +x /usr/local/bin/chromedriver
```

### 3. System Dependencies

```bash
# Install required system libraries
sudo apt install -y \
    libnss3-dev \
    libgconf-2-4 \
    libxss1 \
    libappindicator1 \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libgtk-3-0
```

## Environment Detection

The bot automatically detects the environment using these indicators:

1. **DEBUG environment variable**: `DEBUG=False` indicates production
2. **HTTP_HOST**: Checks for production domains like `jgmanage.business`
3. **SERVER_SOFTWARE**: Detects Passenger server
4. **DISPLAY variable**: Missing DISPLAY indicates headless environment
5. **Virtual environment path**: Server-like paths indicate production

## Configuration Files

### 1. Chrome Configuration Manager (`chrome_config.py`)
- Handles environment detection
- Configures Chrome options for different environments
- Provides anti-detection measures

### 2. Test Script (`test_headless.py`)
- Tests Chrome configuration in both modes
- Verifies headless functionality
- Takes screenshots to confirm operation

## Testing Your Setup

### Local Testing (Force Headless Mode)
```bash
cd external/bots/Barbados_form/
python test_headless.py
```

### Server Testing
```bash
# Set environment variables to simulate production
export DEBUG=False
export DISPLAY=""
python test_headless.py
```

## Shared Hosting Considerations

If you're using shared hosting (like your current setup with Passenger):

### Limitations:
1. **No root access**: Can't install Chrome system-wide
2. **Resource limits**: Browser automation is resource-intensive
3. **Process restrictions**: Long-running processes may be killed

### Solutions:
1. **Contact hosting provider**: Ask about Chrome/browser automation support
2. **Use VPS/dedicated server**: More control over environment
3. **Consider cloud services**: AWS, Google Cloud, DigitalOcean

## Environment Variables for Production

Set these in your production environment:

```bash
# Force production mode
export DEBUG=False
export DJANGO_SETTINGS_MODULE=config.settings

# Optional: Force headless mode
export FORCE_HEADLESS=True
```

## Troubleshooting

### Common Issues:

1. **Chrome not found**:
   ```bash
   which google-chrome
   which chromium-browser
   ```

2. **ChromeDriver version mismatch**:
   ```bash
   google-chrome --version
   chromedriver --version
   ```

3. **Permission issues**:
   ```bash
   sudo chmod +x /usr/local/bin/chromedriver
   ```

4. **Missing dependencies**:
   ```bash
   ldd /usr/bin/google-chrome | grep "not found"
   ```

### Debug Mode:
Enable detailed logging in your bot:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Performance Optimization

For server environments:

1. **Disable images**: Already configured in headless mode
2. **Use minimal window size**: Set to 1920x1080
3. **Disable unnecessary features**: Extensions, plugins, etc.
4. **Monitor resource usage**: CPU and memory consumption

## Security Considerations

1. **Run with limited privileges**: Don't run as root
2. **Network restrictions**: Configure firewall rules
3. **Data protection**: Secure handling of customer data
4. **Log management**: Secure storage of bot logs

## Monitoring and Maintenance

1. **Log rotation**: Prevent log files from growing too large
2. **Health checks**: Regular testing of bot functionality
3. **Chrome updates**: Keep Chrome and ChromeDriver updated
4. **Resource monitoring**: Track CPU, memory, and disk usage

## Next Steps

1. Test the updated bot locally with headless mode
2. Verify your server environment meets requirements
3. Deploy and test on your production server
4. Monitor performance and adjust configuration as needed

For questions or issues, check the bot logs for detailed error messages and environment detection results.
