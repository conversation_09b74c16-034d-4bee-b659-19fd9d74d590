from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from orders.models import order
from .tasks import schedule_job
from .models import QueuedJob, JobTimeline
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=order)
def create_queue_job_on_criminal_check_passed(sender, instance, created, **kwargs):
    """
    When an order's status changes to 'criminal_check_passed',
    create a queued job for it.
    """
    logger.info(f"Signal received for order {instance.id} with status: {instance.status}")
    
    if instance.status == 'criminal_check_passed':
        logger.info(f"Order {instance.id} passed criminal check, creating queue job")
        try:
            # Use apply instead of delay for synchronous execution during debugging
            job_id = schedule_job(str(instance.id))
            logger.info(f"Job created with ID: {job_id}")
            return job_id
        except Exception as e:
            logger.error(f"Failed to schedule job for order {instance.id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    else:
        logger.debug(f"Order status {instance.status} does not trigger job creation")


@receiver(post_save, sender=QueuedJob)
def job_updated(sender, instance, created, **kwargs):
    """Send live update when a job is created or updated and create timeline events"""
    if created:
        # Create initial timeline event for new jobs
        try:
            JobTimeline.objects.create(
                job=instance,
                event_type='created',
                description=f"Job created for order {instance.order.id} ({instance.order.first_name} {instance.order.surname})",
                metadata={
                    'order_id': str(instance.order.id),
                    'location': instance.location.location_name,
                    'customer_name': f"{instance.order.first_name} {instance.order.surname}",
                    'customer_email': instance.order.customer_email
                }
            )

            # Create queued event if status is queued
            if instance.status == 'queued':
                JobTimeline.objects.create(
                    job=instance,
                    event_type='queued',
                    description="Job queued for processing",
                    metadata={
                        'scheduled_for': instance.scheduled_for.isoformat() if instance.scheduled_for else None,
                        'priority_flag': instance.priority_flag
                    }
                )
        except Exception as e:
            logger.error(f"Failed to create timeline events for new job {instance.id}: {str(e)}")

    pass


@receiver(post_delete, sender=QueuedJob)
def job_deleted(sender, instance, **kwargs):
    """Log when a job is deleted"""
    logger.info(f"Job {instance.id} deleted")