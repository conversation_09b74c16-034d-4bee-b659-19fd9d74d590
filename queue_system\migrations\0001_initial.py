# Generated manually on 2025-07-14 - Consolidated migration for queue_system
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    """
    Consolidated migration for queue_system app.
    This migration includes all models and fields in their final state:
    - QueuedJob with all error tracking, review, and requeue fields
    - LocationQueueConfig for location-specific queue settings
    - JobError for error tracking with detailed error information
    - JobTimeline for comprehensive job lifecycle tracking
    """

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('locations', '0007_rename_application_price_location_traveller_price'),
        ('orders', '0013_alter_order_status'),
    ]

    operations = [
        # Create QueuedJob model with all fields
        migrations.CreateModel(
            name='QueuedJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('queued', 'Queued'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('review', 'Under Review'), ('requeued', 'Requeued')], default='queued', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('scheduled_for', models.DateTimeField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('priority_flag', models.BooleanField(default=False)),
                ('worker_id', models.CharField(blank=True, max_length=100, null=True)),
                
                # Error tracking fields
                ('error_message', models.TextField(blank=True, help_text='Last error message', null=True)),
                ('error_details', models.JSONField(blank=True, help_text='Detailed error information', null=True)),
                ('failure_reason', models.CharField(blank=True, help_text='Categorized failure reason', max_length=200, null=True)),
                
                # Review fields
                ('reviewed_by', models.CharField(blank=True, help_text='Admin who reviewed the job', max_length=100, null=True)),
                ('review_notes', models.TextField(blank=True, help_text='Admin notes about the failure', null=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                
                # Requeue fields
                ('requeue_priority', models.BooleanField(default=False, help_text='Process with high priority when requeued')),
                ('requeue_reason', models.TextField(blank=True, help_text='Reason for requeuing', null=True)),
                
                # Foreign keys
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='locations.location')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='queue_jobs', to='orders.order')),
            ],
            options={
                'verbose_name': 'Queued Job',
                'verbose_name_plural': 'Queued Jobs',
                'ordering': ['-priority_flag', 'created_at'],
            },
        ),
        
        # Create LocationQueueConfig model
        migrations.CreateModel(
            name='LocationQueueConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('has_time_window', models.BooleanField(default=False, help_text='If enabled, jobs will only be processed within a specific time window before travel date')),
                ('window_days_before_travel', models.PositiveIntegerField(blank=True, help_text='Number of days before travel date when processing should begin', null=True)),
                ('max_workers', models.PositiveIntegerField(default=1, help_text='Maximum number of concurrent workers for this location')),
                ('active_workers', models.PositiveIntegerField(default=0, help_text='Current number of active workers')),
                ('priority_level', models.PositiveIntegerField(default=1, help_text='Priority level for this location (higher numbers = higher priority)')),
                ('auto_scale', models.BooleanField(default=True, help_text='Automatically scale workers based on queue length')),
                ('min_workers', models.PositiveIntegerField(default=0, help_text='Minimum number of workers to keep running')),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='queue_config', to='locations.location')),
            ],
            options={
                'verbose_name': 'Location Queue Configuration',
                'verbose_name_plural': 'Location Queue Configurations',
            },
        ),

        # Create JobTimeline model
        migrations.CreateModel(
            name='JobTimeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('created', 'Job Created'), ('queued', 'Queued for Processing'), ('processing_started', 'Processing Started'), ('processing_completed', 'Processing Completed'), ('failed', 'Processing Failed'), ('retry_attempted', 'Retry Attempted'), ('moved_to_review', 'Moved to Review Queue'), ('reviewed', 'Reviewed by Admin'), ('requeued', 'Requeued for Processing'), ('cancelled', 'Cancelled'), ('priority_changed', 'Priority Changed'), ('worker_assigned', 'Worker Assigned'), ('worker_released', 'Worker Released'), ('error_occurred', 'Error Occurred'), ('status_changed', 'Status Changed'), ('manual_intervention', 'Manual Intervention')], max_length=30)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField(help_text='Detailed description of the event')),
                ('metadata', models.JSONField(blank=True, help_text='Additional event data (error details, previous values, etc.)', null=True)),
                ('previous_status', models.CharField(blank=True, max_length=20, null=True)),
                ('new_status', models.CharField(blank=True, max_length=20, null=True)),
                ('retry_count_at_event', models.PositiveIntegerField(blank=True, null=True)),
                ('worker_id', models.CharField(blank=True, max_length=100, null=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timeline_events', to='queue_system.queuedjob')),
                ('user', models.ForeignKey(blank=True, help_text='User who triggered this event (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Job Timeline Event',
                'verbose_name_plural': 'Job Timeline Events',
                'ordering': ['-timestamp'],
            },
        ),

        # Create JobError model
        migrations.CreateModel(
            name='JobError',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('occurred_at', models.DateTimeField(auto_now_add=True)),
                ('error_message', models.TextField()),
                ('error_trace', models.TextField(blank=True)),
                ('error_details', models.JSONField(blank=True, help_text='Detailed error information including context and metadata', null=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='errors', to='queue_system.queuedjob')),
            ],
            options={
                'verbose_name': 'Job Error',
                'verbose_name_plural': 'Job Errors',
                'ordering': ['-occurred_at'],
            },
        ),
    ]
