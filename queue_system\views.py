from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils import timezone
from django.db import models
from datetime import timed<PERSON><PERSON>
from .models import QueuedJob, LocationQueueConfig, JobError
from locations.models import Location
import subprocess
import psutil
import signal
import os
import logging

logger = logging.getLogger(__name__)

def get_location_workers(location):
    """Get list of running Celery worker processes for the specified location"""
    try:
        location_workers = []
        queue_name = f'location.{location.id}'
        location_name = location.location_name

        logger.debug(f"Looking for workers for {location_name} with queue {queue_name}")

        # Use a more robust approach to iterate through processes
        for proc in psutil.process_iter():
            try:
                # Get basic process info first
                try:
                    proc_name = proc.name()
                    proc_pid = proc.pid
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, PermissionError):
                    continue

                # Only check celery processes
                if proc_name and 'celery' in proc_name.lower():
                    try:
                        # Try to get command line - this might fail due to permissions
                        cmdline_list = proc.cmdline()
                        cmdline = ' '.join(cmdline_list) if cmdline_list else ''
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, PermissionError):
                        # If we can't get cmdline, skip this process
                        continue

                    logger.debug(f"Checking celery process PID {proc_pid}: {cmdline[:100]}...")

                    # Only count actual worker processes (not beat, flower, etc.)
                    if 'worker' not in cmdline:
                        logger.debug(f"  Skipping - not a worker process")
                        continue

                    # Skip beat schedulers, flower monitors, etc. (use more specific patterns)
                    if any(pattern in cmdline for pattern in [' beat ', 'celery beat', ' flower ', 'celery flower', ' monitor ', ' events ']):
                        logger.debug(f"  Skipping - is beat/flower/monitor/events")
                        continue

                    # Match by queue name (most reliable) or worker name patterns
                    queue_match = queue_name in cmdline
                    admin_match = f'admin_worker_{location_name}' in cmdline
                    auto_match = f'auto_worker_{location_name}' in cmdline
                    celery_match = f'celery_worker_{location_name}' in cmdline

                    logger.debug(f"  Queue match: {queue_match}, Admin match: {admin_match}, Auto match: {auto_match}, Celery match: {celery_match}")

                    if queue_match or admin_match or auto_match or celery_match:
                        logger.debug(f"  MATCH! Adding worker PID {proc_pid}")
                        location_workers.append({
                            'pid': proc_pid,
                            'cmdline': cmdline,
                            'process': proc
                        })
                    else:
                        logger.debug(f"  No match for this location")

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, PermissionError):
                # Skip processes we can't access
                continue
            except Exception as e:
                # Log unexpected errors but continue
                logger.debug(f"Error accessing process: {e}")
                continue

        logger.debug(f"Found {len(location_workers)} workers for {location_name}")
        return location_workers
    except Exception as e:
        logger.error(f"Error finding workers for location {location.location_name}: {e}")
        return []

def stop_location_worker(location):
    """Stop one worker for the specified location"""
    try:
        location_workers = get_location_workers(location)

        if location_workers:
            # Stop the first worker found
            worker = location_workers[0]
            try:
                worker['process'].terminate()
                # Wait a bit for graceful shutdown
                worker['process'].wait(timeout=5)
                logger.info(f"Gracefully stopped worker PID {worker['pid']} for {location.location_name}")
                return f"PID {worker['pid']}"
            except psutil.TimeoutExpired:
                # Force kill if graceful shutdown failed
                worker['process'].kill()
                logger.info(f"Force killed worker PID {worker['pid']} for {location.location_name}")
                return f"PID {worker['pid']} (force killed)"
            except Exception as e:
                logger.error(f"Error stopping worker PID {worker['pid']}: {e}")
                return None
        else:
            logger.warning(f"No workers found for location {location.location_name}")
            return None

    except Exception as e:
        logger.error(f"Error finding workers for location {location.location_name}: {e}")
        return None

def sync_location_worker_count(location):
    """Sync database worker count with actual running workers"""
    try:
        config, created = LocationQueueConfig.objects.get_or_create(
            location=location,
            defaults={'max_workers': 1, 'active_workers': 0}
        )

        # Get actual running workers
        running_workers = get_location_workers(location)
        actual_count = len(running_workers)

        # Update database if different
        if config.active_workers != actual_count:
            logger.info(f"Syncing worker count for {location.location_name}: DB={config.active_workers}, Actual={actual_count}")
            config.active_workers = actual_count
            # Don't reduce max_workers below actual count
            if config.max_workers < actual_count:
                config.max_workers = actual_count
            config.save()

        return actual_count
    except Exception as e:
        logger.error(f"Error syncing worker count for {location.location_name}: {e}")
        return 0

@staff_member_required
def queue_overview(request):
    """Queue Overview Dashboard - Shows active queue length, workers, success rates per location"""
    
    # Get all locations with their queue configs
    locations = Location.objects.all()
    location_stats = []
    
    total_active = 0
    total_waiting = 0
    total_workers = 0
    total_max_workers = 0
    
    for location in locations:
        # Get or create queue config
        config, created = LocationQueueConfig.objects.get_or_create(
            location=location,
            defaults={'max_workers': 1, 'active_workers': 0}
        )
        
        # Active queue (queued + processing + requeued)
        active_count = QueuedJob.objects.filter(
            location=location,
            status__in=['queued', 'processing', 'requeued']
        ).count()
        
        # Waiting queue (jobs scheduled for future)
        waiting_count = QueuedJob.objects.filter(
            location=location,
            status='queued',
            scheduled_for__gt=timezone.now()
        ).count()
        
        # Success/failure rates (last 7 days)
        week_ago = timezone.now() - timedelta(days=7)
        completed_jobs = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=week_ago,
            status='completed'
        ).count()
        
        failed_jobs = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=week_ago,
            status__in=['failed', 'review']
        ).count()
        
        total_jobs = completed_jobs + failed_jobs
        success_rate = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
        
        # Recent completions/failures (last 24 hours)
        day_ago = timezone.now() - timedelta(hours=24)
        recent_completed = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=day_ago,
            status='completed'
        ).count()
        
        recent_failed = QueuedJob.objects.filter(
            location=location,
            completed_at__gte=day_ago,
            status__in=['failed', 'review']
        ).count()
        
        location_stats.append({
            'location': location,
            'config': config,
            'active_queue_length': active_count,
            'waiting_queue_length': waiting_count,
            'active_workers': config.active_workers,
            'max_workers': config.max_workers,
            'success_rate': round(success_rate, 1),
            'recent_completed': recent_completed,
            'recent_failed': recent_failed,
            'total_jobs_week': total_jobs
        })
        
        total_active += active_count
        total_waiting += waiting_count
        total_workers += config.active_workers
        total_max_workers += config.max_workers
    
    # Overall system stats
    overall_stats = {
        'total_active_jobs': total_active,
        'total_waiting_jobs': total_waiting,
        'total_active_workers': total_workers,
        'total_max_workers': total_max_workers,
        'worker_utilization': round((total_workers / total_max_workers * 100) if total_max_workers > 0 else 0, 1)
    }

    # Configuration statistics
    config_stats = {
        'total_locations': LocationQueueConfig.objects.count(),
        'time_window_locations': LocationQueueConfig.objects.filter(has_time_window=True).count(),
        'auto_scale_locations': LocationQueueConfig.objects.filter(auto_scale=True).count(),
        'total_max_workers': LocationQueueConfig.objects.aggregate(total=models.Sum('max_workers'))['total'] or 0,
        'total_active_workers': LocationQueueConfig.objects.aggregate(total=models.Sum('active_workers'))['total'] or 0,
    }

    context = {
        'location_stats': location_stats,
        'overall_stats': overall_stats,
        'config_stats': config_stats,
        'title': 'Queue Overview Dashboard'
    }
    
    return render(request, 'admin/queue_system/queue_overview.html', context)

@staff_member_required
def location_queue_details(request, location_id):
    """Location Queue Details - Shows jobs in active/waiting queues with worker controls"""
    
    location = get_object_or_404(Location, id=location_id)
    config, created = LocationQueueConfig.objects.get_or_create(
        location=location,
        defaults={'max_workers': 1, 'active_workers': 0}
    )

    # Sync worker count with actual running processes
    sync_location_worker_count(location)
    
    # Active queue jobs (queued, processing, requeued)
    active_jobs = QueuedJob.objects.filter(
        location=location,
        status__in=['queued', 'processing', 'requeued']
    ).order_by('-priority_flag', 'created_at')
    
    # Waiting queue jobs (scheduled for future)
    waiting_jobs = QueuedJob.objects.filter(
        location=location,
        status='queued',
        scheduled_for__gt=timezone.now()
    ).order_by('scheduled_for')
    
    # Add age calculation for active jobs
    now = timezone.now()
    for job in active_jobs:
        if job.created_at:
            age = now - job.created_at
            job.age_hours = age.total_seconds() / 3600
    
    context = {
        'location': location,
        'config': config,
        'active_jobs': active_jobs,
        'waiting_jobs': waiting_jobs,
        'title': f'Queue Details - {location.location_name}'
    }
    
    return render(request, 'admin/queue_system/location_queue_details.html', context)

@staff_member_required
def review_queue_dashboard(request):
    """Review Queue Dashboard - Shows failed jobs across all locations with filtering"""
    
    # Get filter parameters
    location_filter = request.GET.get('location')
    error_type_filter = request.GET.get('error_type')
    
    # Base queryset for review queue - include both failed and review status jobs
    review_jobs = QueuedJob.objects.filter(
        status__in=['review', 'failed']
    ).select_related('order', 'location')

    # Apply filters
    if location_filter:
        review_jobs = review_jobs.filter(location_id=location_filter)

    if error_type_filter:
        review_jobs = review_jobs.filter(failure_reason=error_type_filter)

    review_jobs = review_jobs.order_by('-created_at')
    
    # Get filter options
    locations = Location.objects.all()
    error_types = QueuedJob.objects.filter(
        failure_reason__isnull=False
    ).values_list('failure_reason', flat=True).distinct()
    
    # Statistics
    total_review_jobs = QueuedJob.objects.filter(status__in=['review', 'failed']).count()
    reviewed_jobs = QueuedJob.objects.filter(
        status__in=['review', 'failed'],
        reviewed_by__isnull=False
    ).count()
    
    context = {
        'review_jobs': review_jobs,
        'locations': locations,
        'error_types': error_types,
        'selected_location': location_filter,
        'selected_error_type': error_type_filter,
        'total_review_jobs': total_review_jobs,
        'reviewed_jobs': reviewed_jobs,
        'title': 'Review Queue Dashboard'
    }
    
    return render(request, 'admin/queue_system/review_queue_dashboard.html', context)

@staff_member_required
def job_details(request, job_id):
    """Job Details - Complete job history, errors, order data, manual intervention"""
    
    job = get_object_or_404(QueuedJob, id=job_id)
    
    # Get all errors for this job
    job_errors = JobError.objects.filter(job=job).order_by('-occurred_at')

    # Group errors by message to show retry information
    error_groups = {}
    for error in job_errors:
        msg = error.error_message
        if msg not in error_groups:
            error_groups[msg] = []
        error_groups[msg].append(error)

    # Get the detailed error description from the latest error
    detailed_error_description = None
    actual_error_message = None
    failure_reason = None

    if job_errors.exists():
        latest_error = job_errors.first()

        # Use the detailed error description if available
        if latest_error.error_message and not latest_error.error_message.startswith('Retry '):
            detailed_error_description = latest_error.error_message
            actual_error_message = latest_error.error_message

        # Get failure reason from error details
        if latest_error.error_details and 'failure_reason' in latest_error.error_details:
            failure_reason = latest_error.error_details['failure_reason'].replace('_', ' ').title()

        # Fallback to job.error_message if no detailed description
        if not detailed_error_description and job.error_message:
            actual_error_message = job.error_message
            detailed_error_description = job.error_message
    # Format error details as JSON string for template display
    formatted_error_details = None
    if job.error_details:
        import json
        try:
            if isinstance(job.error_details, str):
                # If it's already a string, try to parse and reformat
                parsed = json.loads(job.error_details)
                formatted_error_details = json.dumps(parsed, indent=2, ensure_ascii=False)
            else:
                # If it's a dict or other object, format it as JSON
                formatted_error_details = json.dumps(job.error_details, indent=2, ensure_ascii=False, default=str)
        except (json.JSONDecodeError, TypeError):
            # If JSON parsing fails, convert to string
            formatted_error_details = str(job.error_details)

    context = {
        'job': job,
        'job_errors': job_errors,
        'error_groups': error_groups,
        'actual_error_message': actual_error_message,
        'detailed_error_description': detailed_error_description,
        'failure_reason': failure_reason,
        'formatted_error_details': formatted_error_details,
        'order': job.order,
        'location': job.location,
        'title': f'Job Details - {job.id}'
    }
    
    return render(request, 'admin/queue_system/job_details.html', context)

@staff_member_required
def adjust_workers(request, location_id):
    """AJAX endpoint to adjust worker count for a location"""

    if request.method == 'POST':
        location = get_object_or_404(Location, id=location_id)
        config, created = LocationQueueConfig.objects.get_or_create(
            location=location,
            defaults={'max_workers': 1, 'active_workers': 0}
        )

        action = request.POST.get('action')
        message = ""
        success = False

        try:
            if action == 'increase':
                # Check if we can start more workers (don't exceed max_workers)
                if config.active_workers >= config.max_workers:
                    message = f'Cannot start more workers. Already at maximum ({config.active_workers}/{config.max_workers})'
                    success = False
                else:
                    # Start a new Celery worker for this location
                    queue_name = f'location.{location.id}'
                    worker_name = f'admin_worker_{location.location_name}_{config.active_workers + 1}'

                    cmd = [
                        'celery', '-A', 'config', 'worker',
                        '--concurrency=1',
                        f'--queues={queue_name}',
                        f'--hostname={worker_name}@%h',
                        '--without-gossip',
                        '--without-mingle',
                        '--without-heartbeat'
                    ]

                    # Add detach option only on non-Windows platforms
                    if os.name != 'nt':
                        cmd.append('--detach')

                    try:
                        if os.name == 'nt':  # Windows
                            # On Windows, start process in background with proper flags
                            process = subprocess.Popen(
                                cmd,
                                # stdout=subprocess.DEVNULL,
                                # stderr=subprocess.DEVNULL,
                                # stdin=subprocess.DEVNULL,
                                # creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.DETACHED_PROCESS,
                                # close_fds=True
                            )
                            # Give it a moment to start
                            import time
                            time.sleep(3)

                            # Check if process is still running
                            if process.poll() is None:
                                # Process is running - only increment active_workers, not max_workers
                                config.active_workers += 1
                                config.save()
                                message = f'Started new worker {worker_name} for {location.location_name} ({config.active_workers}/{config.max_workers})'
                                success = True
                                logger.info(f"Started worker {worker_name} (PID: {process.pid}) for location {location.location_name}")
                            else:
                                # Process failed to start - try to get error info
                                message = f'Failed to start worker: Process exited immediately'
                                logger.error(f"Failed to start worker for {location.location_name}: Process exited immediately")
                        else:  # Unix/Linux with detach support
                            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                            if result.returncode == 0:
                                # Only increment active_workers, not max_workers
                                config.active_workers += 1
                                config.save()
                                message = f'Started new worker {worker_name} for {location.location_name} ({config.active_workers}/{config.max_workers})'
                                success = True
                                logger.info(f"Started worker {worker_name} for location {location.location_name}")
                            else:
                                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                                message = f'Failed to start worker: {error_msg}'
                                logger.error(f"Failed to start worker for {location.location_name}: {error_msg}")

                    except subprocess.TimeoutExpired:
                        # Worker likely started but didn't return in time (Unix only)
                        config.active_workers += 1
                        config.save()
                        message = f'Worker {worker_name} started for {location.location_name} ({config.active_workers}/{config.max_workers})'
                        success = True
                        logger.info(f"Worker {worker_name} started (timeout but likely successful)")
                    except Exception as e:
                        message = f'Error starting worker: {str(e)}'
                        logger.error(f"Exception starting worker for {location.location_name}: {e}")

            elif action == 'decrease' and config.active_workers > 0:
                # Decrease workers by stopping one worker process
                success = False
                message = "No workers to stop"

                try:
                    # Find and stop one worker for this location
                    stopped_worker = stop_location_worker(location)

                    if stopped_worker:
                        # Only decrease active_workers, keep max_workers unchanged
                        config.active_workers -= 1
                        config.save()
                        message = f'Stopped worker {stopped_worker} for {location.location_name} ({config.active_workers}/{config.max_workers})'
                        success = True
                    else:
                        message = f'No running workers found for {location.location_name}'

                except Exception as e:
                    message = f'Error stopping worker: {str(e)}'

            elif action == 'set_max':
                new_max = int(request.POST.get('max_workers', 1))
                old_max = config.max_workers
                config.max_workers = max(1, new_max)
                config.active_workers = min(config.active_workers, config.max_workers)
                config.save()

                # If increasing max workers, try to start additional workers
                if new_max > old_max:
                    workers_to_start = new_max - config.active_workers
                    if workers_to_start > 0:
                        # Start additional workers
                        queue_name = f'location.{location.id}'
                        started_workers = 0

                        for i in range(workers_to_start):
                            worker_name = f'admin_worker_{location.location_name}_{config.active_workers + i + 1}'

                            cmd = [
                                'celery', '-A', 'config', 'worker',
                                '--loglevel=info',
                                '--concurrency=1',
                                f'--queues={queue_name}',
                                f'--hostname={worker_name}@%h',
                                '--without-gossip',
                                '--without-mingle',
                                '--without-heartbeat'
                            ]

                            # Add detach option only on non-Windows platforms
                            if os.name != 'nt':
                                cmd.append('--detach')

                            try:
                                if os.name == 'nt':  # Windows
                                    process = subprocess.Popen(
                                        cmd,
                                        stdout=subprocess.DEVNULL,
                                        stderr=subprocess.DEVNULL,
                                        stdin=subprocess.DEVNULL,
                                        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP | subprocess.DETACHED_PROCESS,
                                        close_fds=True
                                    )
                                    time.sleep(2)  # Brief wait
                                    if process.poll() is None:
                                        started_workers += 1
                                else:  # Unix/Linux
                                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                                    if result.returncode == 0:
                                        started_workers += 1
                            except subprocess.TimeoutExpired:
                                started_workers += 1
                            except:
                                pass

                        config.active_workers += started_workers
                        config.save()

                message = f'Set max workers for {location.location_name} to {config.max_workers} (active: {config.active_workers})'
                success = True

            # Sync with actual Celery workers
            try:
                sync_location_worker_count(location)
            except Exception as e:
                logger.warning(f"Failed to sync worker count: {e}")

        except Exception as e:
            message = f'Error: {str(e)}'
            success = False

        return JsonResponse({
            'success': success,
            'message': message,
            'active_workers': config.active_workers,
            'max_workers': config.max_workers
        })

    return JsonResponse({'success': False, 'message': 'Invalid request'})

@staff_member_required
def update_config(request, location_id):
    """AJAX endpoint to update location queue configuration"""

    if request.method == 'POST':
        try:
            import json
            location = get_object_or_404(Location, id=location_id)
            config, created = LocationQueueConfig.objects.get_or_create(
                location=location,
                defaults={'max_workers': 1, 'active_workers': 0}
            )

            # Parse JSON data
            data = json.loads(request.body)

            # Update configuration fields
            config.has_time_window = data.get('has_time_window', False)
            config.window_days_before_travel = data.get('window_days_before_travel')
            config.auto_scale = data.get('auto_scale', True)
            config.min_workers = max(0, int(data.get('min_workers', 0)))
            config.priority_level = max(1, min(5, int(data.get('priority_level', 1))))

            # Validate window_days_before_travel
            if config.has_time_window and config.window_days_before_travel:
                config.window_days_before_travel = max(1, min(365, int(config.window_days_before_travel)))
            elif not config.has_time_window:
                config.window_days_before_travel = None

            # Ensure min_workers doesn't exceed max_workers
            if config.min_workers > config.max_workers:
                config.min_workers = config.max_workers

            config.save()

            return JsonResponse({
                'success': True,
                'message': f'Configuration updated for {location.location_name}',
                'config': {
                    'has_time_window': config.has_time_window,
                    'window_days_before_travel': config.window_days_before_travel,
                    'auto_scale': config.auto_scale,
                    'min_workers': config.min_workers,
                    'priority_level': config.priority_level,
                    'max_workers': config.max_workers,
                    'active_workers': config.active_workers
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': f'Failed to update configuration: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'Invalid request method'})



@staff_member_required
def job_action(request, job_id):
    """Handle manual job interventions (requeue, prioritize, cancel)"""
    
    if request.method == 'POST':
        job = get_object_or_404(QueuedJob, id=job_id)
        action = request.POST.get('action')
        
        if action == 'requeue':
            priority = request.POST.get('priority') == 'true'
            reason = request.POST.get('reason', 'Manual requeue via admin')
            
            job.requeue_job(
                admin_user=request.user,
                priority=priority,
                reason=reason
            )
            messages.success(request, f'Job {job_id} requeued successfully')
            
        elif action == 'prioritize':
            job.priority_flag = True
            job.save()
            messages.success(request, f'Job {job_id} marked as priority')
            
        elif action == 'cancel':
            job.status = 'cancelled'
            job.save()
            messages.success(request, f'Job {job_id} cancelled')
            
        elif action == 'move_to_review':
            notes = request.POST.get('notes', 'Moved to review via admin')
            job.move_to_review(
                admin_user=request.user,
                notes=notes
            )
            messages.success(request, f'Job {job_id} moved to review queue')
    
    # Redirect back to review queue dashboard
    return redirect('queue_system:review_queue_dashboard')




