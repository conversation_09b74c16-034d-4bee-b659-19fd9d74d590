"""
Test script to verify headless Chrome configuration works correctly.
This script tests both development and production modes.
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import os
import logging
import time

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def is_production_environment():
    """
    Detect if running in a production/server environment.
    """
    try:
        production_indicators = [
            not os.environ.get('DEBUG', 'True').lower() in ['true', '1', 'yes'],
            os.environ.get('DJANGO_SETTINGS_MODULE') == 'config.settings' and 
            any(host in os.environ.get('HTTP_HOST', '') for host in ['jgmanage.business', 'production']),
            os.environ.get('SERVER_SOFTWARE', '').lower().startswith('passenger'),
            not os.environ.get('DISPLAY'),
            any(var in os.environ for var in ['CI', 'PASSENGER_APP_ENV', 'SERVER_NAME']),
            '/home/' in os.environ.get('VIRTUAL_ENV', '') and 'virtualenv' in os.environ.get('VIRTUAL_ENV', ''),
        ]
        
        is_prod = any(production_indicators)
        logger.info(f"Environment detection - Production: {is_prod}")
        return is_prod
        
    except Exception as e:
        logger.warning(f"Error detecting environment, defaulting to development mode: {e}")
        return False

def test_chrome_configuration(force_headless=False):
    """
    Test Chrome configuration for both development and production modes.
    """
    driver = None
    try:
        chrome_options = ChromeOptions()
        
        # Detect environment or force headless mode
        is_production = is_production_environment() or force_headless
        
        if is_production:
            # Production/server configuration - headless mode
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            logger.info("Testing Chrome in headless mode")
        else:
            # Development configuration (visible browser)
            chrome_options.add_argument('--window-size=1920,1080')
            logger.info("Testing Chrome in normal mode")
        
        # Common options for both environments
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Initialize the webdriver
        logger.info("Initializing Chrome WebDriver...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # Execute script to remove webdriver property
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        wait = WebDriverWait(driver, 20)
        
        # Test navigation to the Barbados form website
        logger.info("Navigating to Barbados Immigration Form Website...")
        driver.get("https://www.travelform.gov.bb/create")
        
        # Wait for page to load and check title
        logger.info(f"Page title: {driver.title}")
        
        # Try to find a basic element to verify page loaded
        try:
            # Wait for any form element to appear
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "form")))
            logger.info("✅ Page loaded successfully - form element found")
        except Exception as e:
            logger.warning(f"⚠️ Could not find form element, but page loaded: {e}")
        
        # Take a screenshot if in headless mode to verify it's working
        if is_production:
            screenshot_path = "test_headless_screenshot.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Screenshot saved to {screenshot_path}")
        
        logger.info("✅ Chrome configuration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chrome configuration test failed: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()
            logger.info("Chrome WebDriver closed")

def main():
    """
    Run tests for both development and production modes.
    """
    logger.info("🚀 Starting Chrome configuration tests...")
    logger.info("=" * 50)
    
    # Test 1: Current environment detection
    logger.info("Test 1: Testing with current environment detection")
    success1 = test_chrome_configuration(force_headless=False)
    
    logger.info("\n" + "=" * 50)
    
    # Test 2: Force headless mode
    logger.info("Test 2: Testing with forced headless mode")
    success2 = test_chrome_configuration(force_headless=True)
    
    logger.info("\n" + "=" * 50)
    
    if success1 and success2:
        logger.info("🎉 All tests passed! Chrome configuration is working correctly.")
    else:
        logger.error("❌ Some tests failed. Please check the configuration.")
    
    logger.info("Test completed.")

if __name__ == "__main__":
    main()
