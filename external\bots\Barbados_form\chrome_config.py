"""
Chrome WebDriver configuration for different environments.
This module provides standardized Chrome options for development and production environments.
"""

import os
import logging
from selenium.webdriver.chrome.options import Options as ChromeOptions

class ChromeConfigManager:
    """
    Manages Chrome WebDriver configuration for different environments.
    """
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
    
    def is_production_environment(self):
        """
        Detect if the bot is running in a production/server environment.
        Returns True if running on server, False if running in development.
        """
        try:
            # Check multiple indicators for production environment
            production_indicators = [
                # Check if DEBUG is False in Django settings
                not os.environ.get('DEBUG', 'True').lower() in ['true', '1', 'yes'],
                
                # Check if running on known production domains
                os.environ.get('DJANGO_SETTINGS_MODULE') == 'config.settings' and 
                any(host in os.environ.get('HTTP_HOST', '') for host in ['jgmanage.business', 'production']),
                
                # Check for common server environment variables
                os.environ.get('SERVER_SOFTWARE', '').lower().startswith('passenger'),
                
                # Check if display is not available (headless environment)
                not os.environ.get('DISPLAY'),
                
                # Check for common CI/server environment variables
                any(var in os.environ for var in ['CI', 'PASSENGER_APP_ENV', 'SERVER_NAME']),
                
                # Check if running in a virtual environment on a server path
                '/home/' in os.environ.get('VIRTUAL_ENV', '') and 'virtualenv' in os.environ.get('VIRTUAL_ENV', ''),
            ]
            
            # If any production indicator is True, consider it production
            is_prod = any(production_indicators)
            
            self.logger.info(f"Environment detection - Production: {is_prod}")
            self.logger.debug(f"DEBUG: {os.environ.get('DEBUG', 'Not set')}")
            self.logger.debug(f"HTTP_HOST: {os.environ.get('HTTP_HOST', 'Not set')}")
            self.logger.debug(f"DISPLAY: {os.environ.get('DISPLAY', 'Not set')}")
            self.logger.debug(f"SERVER_SOFTWARE: {os.environ.get('SERVER_SOFTWARE', 'Not set')}")
            
            return is_prod
            
        except Exception as e:
            self.logger.warning(f"Error detecting environment, defaulting to development mode: {e}")
            return False
    
    def get_chrome_options(self, force_headless=False):
        """
        Get Chrome options configured for the current environment.
        
        Args:
            force_headless (bool): Force headless mode regardless of environment detection
            
        Returns:
            ChromeOptions: Configured Chrome options
        """
        chrome_options = ChromeOptions()
        
        # Detect if running in production/server environment
        is_production = self.is_production_environment() or force_headless
        
        if is_production:
            # Production/server configuration - headless mode
            self._configure_headless_options(chrome_options)
            self.logger.info("Configured Chrome for headless/server environment")
        else:
            # Development configuration (visible browser)
            self._configure_development_options(chrome_options)
            self.logger.info("Configured Chrome for development environment")
        
        # Apply common options for both environments
        self._configure_common_options(chrome_options)
        
        return chrome_options
    
    def _configure_headless_options(self, chrome_options):
        """Configure Chrome options for headless/server environment."""
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # Faster loading
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-features=TranslateUI')
        chrome_options.add_argument('--disable-ipc-flooding-protection')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    
    def _configure_development_options(self, chrome_options):
        """Configure Chrome options for development environment."""
        chrome_options.add_argument('--window-size=1920,1080')
        # Keep browser visible for debugging
        # Add any development-specific options here
    
    def _configure_common_options(self, chrome_options):
        """Configure common Chrome options for all environments."""
        # Anti-detection measures
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Performance and stability options
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        
        # Logging options
        chrome_options.add_argument('--log-level=3')  # Suppress INFO, WARNING, ERROR
        chrome_options.add_argument('--silent')
    
    def apply_anti_detection_script(self, driver):
        """
        Apply JavaScript to remove webdriver detection.
        
        Args:
            driver: Selenium WebDriver instance
        """
        try:
            # Remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Override the plugins property to use a custom getter
            driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            
            # Override the languages property to use a custom getter
            driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")
            
            self.logger.debug("Applied anti-detection scripts")
            
        except Exception as e:
            self.logger.warning(f"Failed to apply anti-detection scripts: {e}")

# Convenience function for easy import
def get_configured_chrome_options(logger=None, force_headless=False):
    """
    Convenience function to get configured Chrome options.
    
    Args:
        logger: Logger instance (optional)
        force_headless (bool): Force headless mode regardless of environment
        
    Returns:
        ChromeOptions: Configured Chrome options
    """
    config_manager = ChromeConfigManager(logger)
    return config_manager.get_chrome_options(force_headless)

def apply_anti_detection(driver, logger=None):
    """
    Convenience function to apply anti-detection scripts.
    
    Args:
        driver: Selenium WebDriver instance
        logger: Logger instance (optional)
    """
    config_manager = ChromeConfigManager(logger)
    config_manager.apply_anti_detection_script(driver)
